{"name": "test-vue2-project", "version": "1.0.0", "description": "Test Vue 2 project for migration", "main": "src/main.js", "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2", "element-ui": "^2.15.9", "vue-count-to": "^1.0.13", "vuedraggable": "^2.24.3", "vue-splitpane": "^1.0.6", "v-charts": "^1.19.0", "@riophae/vue-treeselect": "^0.4.0"}, "devDependencies": {"vue-template-compiler": "^2.6.14", "@vue/cli-service": "^4.5.15", "webpack": "^4.46.0"}, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}}
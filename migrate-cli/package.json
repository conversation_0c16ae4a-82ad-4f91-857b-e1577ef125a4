{"name": "vue-migration-tools", "version": "1.0.0", "description": "Vue 2 to Vue 3 migration tools", "main": "index.js", "files": ["index.js", "bin/", "src/", "config/"], "bin": {"vue2to3-migrator": "./bin/cli.js", "vue-migrator": "./bin/vue-migrator.js"}, "scripts": {"migrate": "node index.js", "test": "jest", "test:coverage": "jest --coverage", "test:ai": "jest test/aiService.test.js", "test:build": "jest test/buildFixer.test.js", "test:integration": "jest test/integration.test.js"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@vue/eslint-config-standard": "^8.0.1", "ai": "^3.4.33", "axios": "^1.7.7", "commander": "^11.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.28.0", "fs-extra": "^11.2.0", "glob": "^10.4.5", "gogocode": "^1.0.55", "gogocode-plugin-element": "latest", "gogocode-plugin-vue": "latest", "ora": "^5.4.1", "semver": "^7.6.3"}, "devDependencies": {"jest": "^30.0.0"}}
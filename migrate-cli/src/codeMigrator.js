const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const glob = require('glob')
const gogocode = require('gogocode')
const { transform: vueTransform } = require('gogocode-plugin-vue')
const { transform: elementTransform } = require('gogocode-plugin-element')
const MigrationStrategySelector = require('./migrator/migrationStrategySelector');
const MigrationDocGenerator = require('./migrator/migrationDocGenerator');

/**
 * Vue 代码迁移器
 * 支持从源目录迁移到目标目录，或原地修改
 */
class CodeMigrator {
	constructor (inputPath, options = {}) {
		this.inputPath = path.resolve(inputPath)
		this.options = {
			srcDir: 'src',
			outputPath: null, // null 表示原地修改，否则为目标项目路径
			outputSrcDir: 'src', // 目标项目的源码目录
			backupDir: 'backup',
			includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
			excludePatterns: ['node_modules/**', 'dist/**', 'build/**'],
			copyMode: false, // true: 复制文件到目标目录, false: 原地修改
			enableMigrationStrategy: options.enableMigrationStrategy !== false, // 默认启用迁移策略
			aiApiKey: options.aiApiKey, // AI API Key
			generateMigrationDoc: options.generateMigrationDoc !== false, // 默认生成迁移文档
			...options
		}

		// 兼容旧的 projectPath 参数
		this.projectPath = this.inputPath

		// 确定输出路径
		this.outputPath = this.options.outputPath ? path.resolve(this.options.outputPath) : this.inputPath
		this.isOutputMode = this.options.outputPath !== null

		this.stats = {
			total: 0,
			success: 0,
			failed: 0,
			skipped: 0,
			copied: 0,
			failedFiles: []
		}

		// 迁移策略相关
		this.migrationStrategy = null
		this.analysisResult = null
	}

	/**
	 * 执行代码迁移
	 */
	async migrate () {
		try {
			if (this.isOutputMode) {
				console.log(chalk.blue('🔄 开始 Vue 代码迁移 (输入输出模式)...'))
				console.log(chalk.gray(`源路径: ${this.inputPath}`))
				console.log(chalk.gray(`目标路径: ${this.outputPath}`))
			} else {
				console.log(chalk.blue('🔄 开始 Vue 代码迁移 (原地修改模式)...'))
			}

			// 步骤 0: 迁移策略分析和选择（如果启用）
			if (this.options.enableMigrationStrategy) {
				await this.performMigrationStrategyAnalysis()
			}

			// 如果是输出模式，确保目标目录存在
			if (this.isOutputMode) {
				await this.ensureOutputDirectory()
			}

			// 复制 gogocodeTransfer.js 工具文件
			await this.copyGogocodeTransferFile()

			// 创建备份（仅在原地修改模式下）
			if (!this.isOutputMode) {
				// await this.createBackup()
			}

			// 获取需要迁移的文件
			const files = await this.getFilesToMigrate()
			console.log(chalk.gray(`找到 ${files.length} 个文件需要迁移`))

			this.stats.total = files.length

			// 迁移文件
			for (const filePath of files) {
				await this.migrateFile(filePath)
			}

			// 打印统计结果
			this.printMigrationStats()

			return this.stats
		} catch (error) {
			console.error(chalk.red('❌ 代码迁移失败:'), error.message)
			throw error
		}
	}

	/**
	 * 执行迁移策略分析
	 */
	async performMigrationStrategyAnalysis() {
		try {
			console.log(chalk.blue('🎯 开始迁移策略分析...'));

			const strategySelector = new MigrationStrategySelector(this.inputPath, {
				aiApiKey: this.options.aiApiKey,
				verbose: this.options.verbose
			});

			const strategyResult = await strategySelector.selectStrategy();
			this.migrationStrategy = strategyResult.strategy;
			this.analysisResult = strategyResult.analysisResult;

			// 根据策略执行相应的操作
			if (this.migrationStrategy === 'ai-assisted') {
				console.log(chalk.green('🤖 将使用 AI 辅助迁移策略'));
				// AI 辅助策略的具体实现可以在后续的迁移过程中使用
				const migrationPlan = await strategySelector.executeAIAssistedStrategy();
				this.migrationPlan = migrationPlan;
			} else if (this.migrationStrategy === 'documentation-guided') {
				console.log(chalk.yellow('📖 将使用文档指导迁移策略'));

				// 生成迁移指导文档
				if (this.options.generateMigrationDoc) {
					const docGenerator = new MigrationDocGenerator(this.inputPath, this.analysisResult, {
						outputPath: path.join(this.inputPath, 'migration-guide.md')
					});
					await docGenerator.generateMigrationGuide();
				}

				const migrationGuide = await strategySelector.executeDocumentationGuidedStrategy();
				this.migrationGuide = migrationGuide;
			}

			console.log(chalk.green('✅ 迁移策略分析完成'));
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  迁移策略分析失败: ${error.message}`));
			console.log(chalk.gray('将继续使用默认的迁移流程'));
		}
	}

	/**
	 * 确保输出目录存在
	 */
	async ensureOutputDirectory () {
		if (this.isOutputMode) {
			const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
			await fs.ensureDir(outputSrcPath)
			console.log(chalk.gray(`已创建输出目录: ${outputSrcPath}`))
		}
	}

	/**
	 * 复制 gogocodeTransfer.js 工具文件到目标项目
	 *
	 * - '@/utils/gogocodeTransfer'
	 * - vue-count-to to vue3-count-to
	 */
	async copyGogocodeTransferFile () {
		try {
			// gogocodeTransfer.js 源文件路径（在迁移工具中）
			const sourceTransferFile = path.join(__dirname, 'utils', 'gogocodeTransfer.js')

			// 目标路径
			const targetPath = this.isOutputMode ? this.outputPath : this.inputPath
			const targetUtilsDir = path.join(targetPath, this.options.outputSrcDir || this.options.srcDir, 'utils')
			const targetTransferFile = path.join(targetUtilsDir, 'gogocodeTransfer.js')

			// 检查源文件是否存在
			if (await fs.pathExists(sourceTransferFile)) {
				// 确保目标 utils 目录存在
				await fs.ensureDir(targetUtilsDir)

				// 复制文件
				await fs.copy(sourceTransferFile, targetTransferFile)
				console.log(chalk.green(`✅ 已复制 gogocodeTransfer.js 到: ${path.relative(targetPath, targetTransferFile)}`))
			} else {
				console.log(chalk.yellow(`⚠️  未找到 gogocodeTransfer.js 源文件: ${sourceTransferFile}`))
			}
		} catch (error) {
			console.log(chalk.yellow(`⚠️  复制 gogocodeTransfer.js 失败: ${error.message}`))
		}
	}

	/**
	 * 获取需要迁移的文件列表
	 */
	async getFilesToMigrate () {
		const srcPath = path.join(this.inputPath, this.options.srcDir)
		const files = []

		// 检查源目录是否存在
		if (!await fs.pathExists(srcPath)) {
			throw new Error(`源目录不存在: ${srcPath}`)
		}

		for (const pattern of this.options.includePatterns) {
			const matchedFiles = glob.sync(pattern, {
				cwd: srcPath,
				ignore: this.options.excludePatterns,
				absolute: false
			})

			files.push(...matchedFiles.map(file => path.join(srcPath, file)))
		}

		// 去重
		return [...new Set(files)]
	}

	/**
	 * 迁移单个文件
	 */
	async migrateFile (filePath) {
		try {
			const relativePath = path.relative(this.inputPath, filePath)
			process.stdout.write(chalk.gray(`迁移: ${relativePath} ... `))

			// 读取文件内容
			const source = await fs.readFile(filePath, 'utf8')

			// 根据文件类型选择迁移策略
			let transformedCode
			const ext = path.extname(filePath)

			if (ext === '.vue') {
				transformedCode = await this.migrateVueFile(source, filePath)
			} else if (ext === '.js' || ext === '.ts') {
				transformedCode = await this.migrateJsFile(source, filePath)
			} else {
				console.log(chalk.yellow('跳过'))
				this.stats.skipped++
				return
			}

			// 确定输出文件路径
			const outputFilePath = this.getOutputFilePath(filePath)

			// 写入转换后的代码
			if (transformedCode && (transformedCode !== source || this.isOutputMode)) {
				// 确保输出目录存在
				await fs.ensureDir(path.dirname(outputFilePath))

				await fs.writeFile(outputFilePath, transformedCode, 'utf8')

				if (this.isOutputMode && outputFilePath !== filePath) {
					console.log(chalk.green('✅ (复制+转换)'))
					this.stats.copied++
				} else {
					console.log(chalk.green('✅'))
				}
				this.stats.success++
			} else {
				if (this.isOutputMode) {
					// 输出模式下，即使没有变化也要复制文件
					await fs.ensureDir(path.dirname(outputFilePath))
					await fs.copy(filePath, outputFilePath)
					console.log(chalk.gray('复制（无变化）'))
					this.stats.copied++
				} else {
					console.log(chalk.gray('跳过（无变化）'))
				}
				this.stats.skipped++
			}

		} catch (error) {
			console.log(chalk.red('❌'))
			this.stats.failed++

			// 记录详细的失败信息，包括绝对路径
			const failureInfo = {
				file: path.relative(this.inputPath, filePath),
				absolutePath: filePath,
				error: error.message,
				errorType: this.categorizeError(error.message),
				timestamp: new Date().toISOString()
			}

			this.stats.failedFiles.push(failureInfo)

			// 如果是 Gogocode 相关错误，记录更多信息
			if (error.message.includes('gogocode') || error.message.includes('eventsApi')) {
				console.log(chalk.yellow(`    Gogocode 转换错误: ${failureInfo.file}`))
			}
		}
	}

	/**
	 * 获取输出文件路径
	 */
	getOutputFilePath (inputFilePath) {
		if (!this.isOutputMode) {
			return inputFilePath
		}

		// 计算相对于输入源目录的路径
		const inputSrcPath = path.join(this.inputPath, this.options.srcDir)
		const relativeToSrc = path.relative(inputSrcPath, inputFilePath)

		// 构建输出路径
		const outputSrcPath = path.join(this.outputPath, this.options.outputSrcDir)
		return path.join(outputSrcPath, relativeToSrc)
	}

	/**
	 * 迁移 Vue 文件
	 */
	async migrateVueFile (source, filePath) {
		try {
			let transformedCode = source

			// 使用 gogocode-plugin-vue 进行转换
			try {
				// 正确的 vueTransform API 调用方式
				transformedCode = vueTransform(
					{
						path: filePath,
						source: source,
					},
					{
						gogocode: gogocode,
					},
					{
						rootPath: path.resolve(this.projectPath, this.options.srcDir),
						outFilePath: filePath,
						outRootPath: path.dirname(filePath),
					}
				)

				// vueTransform 直接返回转换后的代码字符串
				if (typeof transformedCode !== 'string') {
					transformedCode = source // 如果转换失败，使用原始代码
				}

			} catch (vueError) {
				console.warn(chalk.yellow(`Vue 转换警告: ${vueError.message}`))
				transformedCode = source // 转换失败时保持原始代码
			}

			// 应用 Element UI 到 Element Plus 的转换
			if (transformedCode.includes('element-ui') || transformedCode.includes('el-')) {
				try {
					// 正确的 elementTransform API 调用方式
					transformedCode = elementTransform(
						{
							path: filePath,
							source: transformedCode,
						},
						{
							gogocode: gogocode,
						},
						{
							rootPath: path.resolve(this.projectPath, this.options.srcDir),
							outFilePath: filePath,
							outRootPath: path.dirname(filePath),
						}
					)

					// elementTransform 直接返回转换后的代码字符串
					if (typeof transformedCode !== 'string') {
						transformedCode = source // 如果转换失败，使用原始代码
					}

				} catch (elementError) {
					// Element 转换失败不应该阻止整个流程
					console.warn(chalk.yellow(`Element UI 转换警告: ${elementError.message}`))
				}
			}

			// 应用自定义转换
			try {
				transformedCode = this.applyCustomVueTransforms(transformedCode)
			} catch (customError) {
				console.warn(chalk.yellow(`自定义 Vue 转换警告: ${customError.message}`))
			}

			return transformedCode
		} catch (error) {
			throw new Error(`Vue 文件转换失败: ${error.message}`)
		}
	}

	/**
	 * 迁移 JS/TS 文件
	 */
	async migrateJsFile (source, filePath) {
		try {
			// 使用 gogocode 进行 AST 转换
			const ast = gogocode(source)

			// 应用 JS 转换规则
			this.applyJsTransforms(ast)

			return ast.generate()
		} catch (error) {
			throw new Error(`JS 文件转换失败: ${error.message}`)
		}
	}

	/**
	 * 应用自定义 Vue 转换规则
	 */
	applyCustomVueTransforms (code) {
		// 替换 Element UI 导入
		code = code.replace(
			/import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['"]/g,
			'import { $1 } from \'element-plus\''
		)

		// 替换 Element UI CSS 导入
		code = code.replace(
			/import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['"]/g,
			'import \'element-plus/dist/index.css\''
		)

		// 替换 Vue 2 的全局 API
		code = code.replace(/Vue\.extend\(/g, 'defineComponent(')
		code = code.replace(/Vue\.component\(/g, 'app.component(')
		code = code.replace(/Vue\.use\(/g, 'app.use(')

		// 替换 $refs 访问方式（需要更复杂的 AST 处理）
		// 这里只做简单的字符串替换示例

		return code
	}

	applyJsTransforms (ast) {
		ast.replace(
			'import countTo from \'vue-count-to\'',
			'import countTo from \'vue3-count-to\''
		)

		// vue-count-to -> vue3-count-to
		// https://github.com/xiaofan9/vue-count-to

		// vue-splitpane -> splitpanes
		// https://github.com/antoniandre/splitpanes

		// vuedraggable -> vue.draggable.next
		// https://github.com/SortableJS/vue.draggable.next

		/// vue-calendar-component, vue-sweet-calendar -> Element Plus
		/// import type { CalendarDateType, CalendarInstance } from 'element-plus'
		/// https://element-plus.org/zh-CN/component/calendar.html


		// v-charts -> vue-charts
		// https://github.com/ecomfe/vue-echarts

		// vue-scrollbars -> vue3-perfect-scrollbar
		// https://github.com/mercs600/vue3-perfect-scrollbar

		/**
		 * vue-uuid -> vue3-uuid,
		 * https://github.com/wang90/vue3-uuid
		 * ```vue
		 * import UUID from "vue3-uuid";
		 * const app = createApp(App)
		 * app.use(UUID)
		 * ```
 		 */

		/**
		 * @riophae/vue-treeselect -> https://element-plus.org/zh-CN/component/tree-select/
		 *
		 * ```vue3
		 * <template>
		 *   <el-tree-select v-model="value" :data="data" style="width: 240px" />
		 * </template>
		 * ```
		 */

		/**
		 * @tinymce/tinymce-vue -> @tinymce/tinymce-vue
		 * https://github.com/tinymce/tinymce-vue
		 */

		/**
		 * @wangeditor/editor-for-vue -> @wangeditor/editor, @wangeditor/editor-for-vue@next
		 * https://www.wangeditor.com/v5/for-frame.html
		 */

		/**
		 * vue-text-format, 同时支持 https://github.com/13601313270/vue-format
		 * ```vue 3
		 *	import Vue, { createApp } from 'vue'
		 *	import format from 'vue-text-format';
		 *	import App from './App.vue';
		 *	const app = createApp(App)
		 *
		 *	app
		 *	  .use(format)
		 *	  .mount('#app')
		 * ```
		 */

		/**
		 * vuepdf -> vue3-pdfjs
		 * https://github.com/randolphtellis/vue3-pdfjs
		 * ```
		 * import { createApp } from 'vue'
		 * import App from './App.vue'
		 * import VuePdf from 'vue3-pdfjs'
		 *
		 * const app = createApp(App)
		 * app.use(VuePdf)
		 * app.mount('#app')
		 * ```
		 */

		// vue-json-pretty, (Vue 2 use vue-json-pretty@v1-latest)
		// https://www.npmjs.com/package/vue-json-pretty

		// vue2-tree-org -> vue3-tree-org
		// https://gitee.com/sangtian152/vue3-tree-org

		// vue-template-compiler ->  @vue/compiler-sfc
		// https://www.npmjs.com/package/@vue/compiler-sfc

		return ast
	}

	/**
	 * 打印迁移统计
	 */
	printMigrationStats () {
		console.log('\n' + chalk.bold('📊 代码迁移统计:'))
		console.log(`总计: ${this.stats.total} 个文件`)
		console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`))
		console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`))
		console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`))

		if (this.isOutputMode) {
			console.log(chalk.blue(`📁 复制: ${this.stats.copied} 个文件`))
			console.log(chalk.gray(`输出路径: ${this.outputPath}`))
		}

		if (this.stats.failedFiles.length > 0) {
			console.log(chalk.red('\n失败的文件:'))
			this.stats.failedFiles.forEach(({ file, error }) => {
				console.log(`  ${file}: ${error}`)
			})
		}

		const successRate = ((this.stats.success / this.stats.total) * 100).toFixed(1)
		console.log(chalk.bold(`\n成功率: ${successRate}%`))
	}

	/**
	 * 获取失败的文件列表（用于 AI 修复）
	 */
	getFailedFiles() {
		return this.stats.failedFiles
	}

	/**
	 * 获取迁移策略
	 */
	getMigrationStrategy() {
		return this.migrationStrategy
	}

	/**
	 * 获取分析结果
	 */
	getAnalysisResult() {
		return this.analysisResult
	}

	/**
	 * 获取迁移计划（AI 辅助策略）
	 */
	getMigrationPlan() {
		return this.migrationPlan
	}

	/**
	 * 获取迁移指南（文档指导策略）
	 */
	getMigrationGuide() {
		return this.migrationGuide
	}

	/**
	 * 分类错误类型
	 */
	categorizeError (errorMessage) {
		if (errorMessage.includes('eventsApi') || errorMessage.includes('gogocode-plugin-vue')) {
			return 'gogocode-events-api'
		} else if (errorMessage.includes('gogocode')) {
			return 'gogocode-general'
		} else if (errorMessage.includes('SyntaxError')) {
			return 'syntax-error'
		} else if (errorMessage.includes('Cannot read properties')) {
			return 'property-access-error'
		} else {
			return 'unknown'
		}
	}

	/**
	 * 获取失败的文件列表
	 */
	getFailedFiles () {
		return this.stats.failedFiles
	}
}

module.exports = CodeMigrator

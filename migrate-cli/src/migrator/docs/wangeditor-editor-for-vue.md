---
title: '从 @wangeditor/editor-for-vue 迁移到 @wangeditor/editor-for-vue@next'
source: '@wangeditor/editor-for-vue'
target: '@wangeditor/editor, @wangeditor/editor-for-vue@next'
---

`wangEditor` v5 对其架构进行了重要更新，将核心编辑器与 Vue 框架的封装进行了解耦。迁移到适用于 Vue 3 的版本需要更新依赖，并采用新的双组件（编辑器和工具栏）模式。

## 1. 依赖更新

首先，你需要卸载旧的 `wangEditor` 包，然后安装新的核心包和 Vue 3 专用的组件包。

```bash
npm uninstall @wangeditor/editor-for-vue
# 同时确保卸载了旧的 v4 核心包（如果存在）
# npm uninstall @wangeditor/editor

# 安装 v5 核心包和 Vue 3 组件
npm install @wangeditor/editor @wangeditor/editor-for-vue@next
```

## 2. 引入样式

新版本的 CSS 文件位于核心包 `@wangeditor/editor` 中。在你的主文件（如 `main.js`）或根组件中引入它。

```javascript
// 在 main.js 或 App.vue 中
import '@wangeditor/editor/dist/css/style.css';
```

## 3. 组件用法变更

`wangEditor` v5 将编辑器（`Editor`）和工具栏（`Toolbar`）拆分为两个独立的组件。你需要在模板中同时使用它们，并通过一个 `ref` 将它们关联起来。

### Vue 2 / 旧版用法

在旧版本中，通常只有一个组件包含了工具栏和编辑器。

```html
<!-- 旧版可能的使用方式 (示意) -->
<template>
  <wangeditor v-model="html" :config="config" />
</template>
```

### Vue 3 全新用法

在 Vue 3 中，你需要分别渲染 `<Toolbar>` 和 `<Editor>`，并使用 `shallowRef` 来持有编辑器实例。

```html
<template>
  <div style="border: 1px solid #ccc">
    <!-- 工具栏 -->
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      mode="default"
    />
    <!-- 编辑器 -->
    <Editor
      style="height: 500px; overflow-y: hidden;"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      mode="default"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<script setup>
import { ref, shallowRef, onBeforeUnmount } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref('<p>hello</p>');

// 工具栏配置
const toolbarConfig = {};

// 编辑器配置
const editorConfig = { 
  placeholder: '请输入内容...',
  // 注意：v5 中，生命周期函数和其他事件要通过 @ 绑定到 <Editor> 组件上，不能写在这里
};

// 编辑器创建回调
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

// 编辑器内容变化回调
const handleChange = (editor) => {
  console.log('content', editor.getHtml());
};

// 组件销毁时，也及时销毁编辑器，重要！
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>
```

## 4. 关键变更点总结

1.  **包分离**: 现在需要安装 `@wangeditor/editor`（核心）和 `@wangeditor/editor-for-vue@next`（Vue 3 组件）两个包。
2.  **组件拆分**: UI 从单个组件拆分为 `<Toolbar>` 和 `<Editor>` 两个组件。
3.  **实例管理**: 必须使用 `shallowRef` 来存储编辑器实例，以避免不必要的性能开销。
4.  **生命周期与事件**: 所有如 `onCreated`, `onChange`, `onFocus` 等生命周期函数都从 `editorConfig` 中移除，改为通过 `@` 语法在 `<Editor>` 组件上进行监听。
5.  **销毁实例**: 必须在组件卸载前（`onBeforeUnmount`）调用 `editor.destroy()` 来销毁编辑器实例，防止内存泄漏。
6.  **配置传递**: 工具栏和编辑器的配置分别通过 `:defaultConfig` prop 传递给各自的组件。

这次迁移涉及架构上的较大变动，请务必仔细按照新的使用方式进行重构。 
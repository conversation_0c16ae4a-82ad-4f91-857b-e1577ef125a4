const path = require('path');
const fs = require('fs');

/**
 * Get the path to the migrator documentation file for a given dependency.
 * @param {string} dependencyName - The name of the dependency.
 * @returns {string|null} The path to the documentation file, or null if not found.
 */
function getMigratorDocPath(dependencyName) {
  let sanitizedName = dependencyName;
  if (sanitizedName.startsWith('@')) {
    sanitizedName = sanitizedName.substring(1).replace(/\//g, '-');
  }

  const docPath = path.resolve(__dirname, 'docs', `${sanitizedName}.md`);

  if (fs.existsSync(docPath)) {
    return docPath;
  }

  return null;
}

/**
 * Get the content of the migrator documentation file for a given dependency.
 * @param {string} dependencyName - The name of the dependency.
 * @returns {string|null} The content of the documentation file, or null if not found.
 */
function getMigratorDocContent(dependencyName) {
  const docPath = getMigratorDocPath(dependencyName);
  if (docPath) {
    try {
      return fs.readFileSync(docPath, 'utf8');
    } catch (error) {
      console.warn(`Warning: Could not read migration doc for ${dependencyName}: ${error.message}`);
      return null;
    }
  }
  return null;
}

/**
 * Get all available migration documentation files.
 * @returns {Array<{name: string, path: string}>} Array of available migration docs.
 */
function getAllMigratorDocs() {
  const docsDir = path.resolve(__dirname, 'docs');

  if (!fs.existsSync(docsDir)) {
    return [];
  }

  try {
    const files = fs.readdirSync(docsDir);
    return files
      .filter(file => file.endsWith('.md'))
      .map(file => ({
        name: file.replace('.md', ''),
        path: path.join(docsDir, file)
      }));
  } catch (error) {
    console.warn(`Warning: Could not read migration docs directory: ${error.message}`);
    return [];
  }
}

module.exports = {
  getMigratorDocPath,
  getMigratorDocContent,
  getAllMigratorDocs
};
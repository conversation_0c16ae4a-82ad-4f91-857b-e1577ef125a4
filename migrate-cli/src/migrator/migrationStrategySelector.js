const chalk = require('chalk');
const ComponentUsageAnalyzer = require('./componentUsageAnalyzer');
const { AIService } = require('../ai/ai-service');
const { getMigratorDocContent } = require('./migrator-docs');

/**
 * 智能迁移策略选择器
 * 根据用户是否有 AI token 来选择迁移策略：
 * - 有 AI 时使用智能分析+AI 翻译
 * - 无 AI 时使用文档指导
 */
class MigrationStrategySelector {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      aiApiKey: options.aiApiKey,
      verbose: options.verbose || false,
      ...options
    };

    this.aiService = new AIService({ apiKey: this.options.aiApiKey });
    this.analyzer = new ComponentUsageAnalyzer(projectPath);
    
    this.strategy = null;
    this.analysisResult = null;
  }

  /**
   * 选择迁移策略
   */
  async selectStrategy() {
    try {
      console.log(chalk.blue('🎯 正在选择最佳迁移策略...'));

      // 1. 分析项目组件使用情况
      this.analysisResult = await this.analyzer.analyze();

      // 2. 检查 AI 服务可用性
      const aiAvailable = this.aiService.isEnabled();

      // 3. 根据条件选择策略
      if (aiAvailable && this.shouldUseAIStrategy()) {
        this.strategy = 'ai-assisted';
        console.log(chalk.green('✅ 选择策略: AI 辅助迁移'));
      } else {
        this.strategy = 'documentation-guided';
        console.log(chalk.yellow('📖 选择策略: 文档指导迁移'));
        if (!aiAvailable) {
          console.log(chalk.gray('   原因: AI 服务不可用'));
        }
      }

      return {
        strategy: this.strategy,
        analysisResult: this.analysisResult,
        aiAvailable
      };
    } catch (error) {
      console.error(chalk.red('❌ 策略选择失败:'), error.message);
      throw error;
    }
  }

  /**
   * 判断是否应该使用 AI 策略
   */
  shouldUseAIStrategy() {
    const { summary } = this.analysisResult;
    
    // 如果有很多没有文档的依赖，AI 策略更有用
    const hasComplexMigration = summary.dependenciesWithoutDocs > 0;
    
    // 如果有很多代码使用，AI 可以提供更精确的迁移
    const hasExtensiveUsage = summary.usageFound > 5;
    
    return hasComplexMigration || hasExtensiveUsage;
  }

  /**
   * 执行 AI 辅助迁移策略
   */
  async executeAIAssistedStrategy() {
    if (this.strategy !== 'ai-assisted') {
      throw new Error('当前策略不是 AI 辅助迁移');
    }

    console.log(chalk.blue('🤖 开始执行 AI 辅助迁移...'));

    const migrationPlan = {
      dependencies: [],
      codeChanges: [],
      recommendations: []
    };

    // 1. 为每个依赖生成 AI 迁移建议
    for (const dep of this.analysisResult.dependencies) {
      const aiSuggestion = await this.generateAIMigrationSuggestion(dep);
      migrationPlan.dependencies.push({
        dependency: dep,
        aiSuggestion
      });
    }

    // 2. 为代码使用生成 AI 修改建议
    const usageGroups = this.groupUsageByFile();
    for (const [filePath, usages] of Object.entries(usageGroups)) {
      const codeChanges = await this.generateAICodeChanges(filePath, usages);
      migrationPlan.codeChanges.push({
        file: filePath,
        usages,
        changes: codeChanges
      });
    }

    // 3. 生成总体建议
    migrationPlan.recommendations = await this.generateAIRecommendations();

    return migrationPlan;
  }

  /**
   * 执行文档指导迁移策略
   */
  async executeDocumentationGuidedStrategy() {
    if (this.strategy !== 'documentation-guided') {
      throw new Error('当前策略不是文档指导迁移');
    }

    console.log(chalk.blue('📖 开始执行文档指导迁移...'));

    const migrationGuide = {
      dependencies: [],
      manualSteps: [],
      resources: []
    };

    // 1. 为有文档的依赖提供迁移指导
    for (const dep of this.analysisResult.dependencies) {
      if (dep.hasDoc) {
        const docContent = getMigratorDocContent(dep.name);
        migrationGuide.dependencies.push({
          dependency: dep,
          documentation: docContent,
          usageLocations: this.getUsageLocations(dep.name)
        });
      } else {
        // 为没有文档的依赖提供通用建议
        migrationGuide.dependencies.push({
          dependency: dep,
          documentation: null,
          generalAdvice: this.generateGeneralMigrationAdvice(dep),
          usageLocations: this.getUsageLocations(dep.name)
        });
      }
    }

    // 2. 生成手动操作步骤
    migrationGuide.manualSteps = this.generateManualSteps();

    // 3. 提供相关资源
    migrationGuide.resources = this.generateResourceLinks();

    return migrationGuide;
  }

  /**
   * 生成 AI 迁移建议
   */
  async generateAIMigrationSuggestion(dependency) {
    const prompt = this.buildAIMigrationPrompt(dependency);
    
    try {
      const suggestion = await this.aiService.callAI(prompt, {
        maxTokens: 1000,
        temperature: 0.3
      });
      
      return {
        success: true,
        suggestion,
        confidence: 'high'
      };
    } catch (error) {
      console.warn(chalk.yellow(`警告: 无法为 ${dependency.name} 生成 AI 建议: ${error.message}`));
      return {
        success: false,
        error: error.message,
        fallback: this.generateGeneralMigrationAdvice(dependency)
      };
    }
  }

  /**
   * 构建 AI 迁移提示词
   */
  buildAIMigrationPrompt(dependency) {
    const usageLocations = this.getUsageLocations(dependency.name);
    const docContent = getMigratorDocContent(dependency.name);

    let prompt = `请为 Vue 2 到 Vue 3 项目迁移提供 ${dependency.name} 的迁移建议。

**依赖信息**:
- 包名: ${dependency.name}
- 当前版本: ${dependency.version}
- 分类: ${dependency.category}

**使用情况**:
${usageLocations.length > 0 ? 
  usageLocations.map(loc => `- ${loc.file}:${loc.line} - ${loc.content}`).join('\n') :
  '- 在代码中未找到直接使用'
}`;

    if (docContent) {
      prompt += `\n\n**现有迁移文档**:\n${docContent.substring(0, 1000)}...`;
    }

    prompt += `\n\n请提供：
1. 具体的迁移步骤
2. 代码修改建议
3. 注意事项
4. 推荐的替代包（如果需要）

请用中文回答，并提供具体可执行的建议。`;

    return prompt;
  }

  /**
   * 生成 AI 代码修改建议
   */
  async generateAICodeChanges(filePath, usages) {
    // 这里可以实现更复杂的代码分析和 AI 建议生成
    // 暂时返回基础结构
    return {
      file: filePath,
      suggestedChanges: [],
      confidence: 'medium'
    };
  }

  /**
   * 生成 AI 总体建议
   */
  async generateAIRecommendations() {
    return [
      '建议先迁移核心依赖（Vue、Vue Router、Vuex）',
      '然后迁移 UI 库（Element UI 到 Element Plus）',
      '最后处理其他工具库和组件'
    ];
  }

  /**
   * 按文件分组使用情况
   */
  groupUsageByFile() {
    const groups = {};
    
    for (const usage of this.analysisResult.usageInCode) {
      if (!groups[usage.file]) {
        groups[usage.file] = [];
      }
      groups[usage.file].push(usage);
    }
    
    return groups;
  }

  /**
   * 获取依赖的使用位置
   */
  getUsageLocations(dependencyName) {
    return this.analysisResult.usageInCode.filter(usage => 
      usage.dependency === dependencyName
    );
  }

  /**
   * 生成通用迁移建议
   */
  generateGeneralMigrationAdvice(dependency) {
    const advice = {
      steps: [],
      warnings: [],
      resources: []
    };

    // 根据依赖类型生成通用建议
    switch (dependency.category) {
      case 'vue-core':
        advice.steps.push('检查 Vue 3 兼容性');
        advice.steps.push('更新到最新版本');
        break;
      case 'ui-library':
        advice.steps.push('查找 Vue 3 兼容版本');
        advice.steps.push('更新组件使用方式');
        break;
      default:
        advice.steps.push('检查官方文档');
        advice.steps.push('寻找 Vue 3 替代方案');
    }

    advice.warnings.push('请仔细测试迁移后的功能');
    advice.resources.push('Vue 3 官方迁移指南');

    return advice;
  }

  /**
   * 生成手动操作步骤
   */
  generateManualSteps() {
    return [
      '1. 备份当前项目',
      '2. 更新 package.json 中的依赖版本',
      '3. 运行 npm install 安装新依赖',
      '4. 根据文档逐个迁移组件',
      '5. 测试所有功能',
      '6. 修复发现的问题'
    ];
  }

  /**
   * 生成资源链接
   */
  generateResourceLinks() {
    return [
      'Vue 3 官方文档: https://v3.vuejs.org/',
      'Vue 3 迁移指南: https://v3-migration.vuejs.org/',
      'Element Plus 文档: https://element-plus.org/',
      'Vue 3 生态系统: https://github.com/vuejs/awesome-vue'
    ];
  }

  /**
   * 获取当前策略
   */
  getStrategy() {
    return this.strategy;
  }

  /**
   * 获取分析结果
   */
  getAnalysisResult() {
    return this.analysisResult;
  }
}

module.exports = MigrationStrategySelector;

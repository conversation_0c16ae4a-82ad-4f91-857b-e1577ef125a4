# Vue 2 到 Vue 3 迁移工具

一个全自动化的 Vue 2 到 Vue 3 迁移工具，支持传统原地迁移和新工程迁移两种模式，使用 Gogocode 进行代码转换，结合 AI 修复复杂问题。

## ✨ 功能特性

### 🎯 智能迁移策略分析（新功能）
- 🔍 **项目分析**: 自动分析项目中使用的组件和依赖
- 🤖 **AI 策略**: 有 AI token 时使用智能分析+AI 翻译
- 📖 **文档策略**: 无 AI token 时生成详细的迁移指导文档
- 📋 **迁移计划**: 根据分析结果生成个性化的迁移计划
- ⏱️ **时间估算**: 智能评估迁移复杂度和所需时间

### 🚀 新工程迁移模式（推荐）
- 📦 **Package 对比**: 智能对比新旧工程的 package.json 差异
- 🧩 **组件迁移**: 专门的组件迁移器，支持组件类型识别和特定转换
- 📄 **视图迁移**: 专门的视图迁移器，支持页面类型识别和路由检测
- 🔒 **安全迁移**: 不修改原项目，将转换后的代码复制到新工程
- 📊 **详细统计**: 提供迁移统计和类型分布分析

### 🔄 传统迁移模式
- 🔄 **自动升级依赖**: 将 Vue 2 相关依赖升级到 Vue 3 兼容版本
- 🔍 **兼容性检查**: 检查项目依赖的 Vue 3 兼容性
- 🤖 **智能代码迁移**: 使用 Gogocode 批量转换 Vue 文件和 JS 文件
- 📝 **失败记录**: 详细记录转换失败的文件和原因
- 🧠 **AI 修复**: 使用 AI 修复 Gogocode 无法处理的复杂情况
- 🔧 **ESLint 修复**: 自动修复代码格式和语法问题
- 🏗️ **构建修复**: 自动构建项目并修复构建错误

## 📦 安装

```bash
# 克隆项目
git clone <repository-url>
cd migrate-cli

# 安装依赖
npm install

# 全局安装（可选）
npm link
```

## 🚀 快速开始

### 🎯 智能策略分析（推荐第一步）

在开始迁移之前，建议先进行项目分析，获得个性化的迁移策略和指导：

```bash
# 分析项目并生成迁移策略
node bin/vue-migrator.js analyze [项目路径]

# 使用 AI 进行智能分析（需要配置 AI token）
node bin/vue-migrator.js analyze --ai-key your_ai_key

# 指定输出文档路径
node bin/vue-migrator.js analyze --output custom-migration-guide.md

# 显示详细分析过程
node bin/vue-migrator.js analyze --verbose
```

**分析结果包括**：
- 📊 项目依赖分析和迁移复杂度评估
- 📖 详细的迁移指导文档（包含分步指南）
- 🎯 基于项目特点的个性化迁移策略
- ⏱️ 迁移时间估算和资源规划建议

### 🌟 新工程迁移模式（推荐）

这是推荐的迁移方式：先创建一个 Vue 3 工程，然后逐步将代码迁移过去。

```bash
# 查看所有可用命令
node bin/vue-migrator.js steps

# 完整迁移：从旧工程迁移到新工程
node bin/vue-migrator.js migrate-to <旧工程路径> <新工程路径>

# 预览模式（不实际修改文件）
node bin/vue-migrator.js migrate-to <旧工程路径> <新工程路径> --dry-run

# 仅对比 package.json
node bin/vue-migrator.js compare <旧工程路径> <新工程路径>

# 仅迁移组件
node bin/vue-migrator.js migrate-components <旧工程路径> <新工程路径>

# 仅迁移视图
node bin/vue-migrator.js migrate-views <旧工程路径> <新工程路径>
```

### 🔄 传统迁移模式

原地修改项目文件的传统方式：

```bash
# 完整迁移（7个步骤）
node bin/vue-migrator.js migrate [项目路径]

# 执行单个步骤
node bin/vue-migrator.js step <1-7> [项目路径]

# 跳过某些步骤
node bin/vue-migrator.js migrate --skip-ai --skip-build

# 预览模式
node bin/vue-migrator.js migrate --dry-run
```

## 📋 迁移策略对比

### 🌟 新工程迁移模式（推荐）

**适用场景**：
- 希望保持原项目不变，降低迁移风险
- 需要逐步验证迁移效果
- 团队协作，需要并行开发

**迁移流程**：
1. **对比分析** - 分析新旧工程的 package.json 差异
2. **组件迁移** - 将 components 目录转换并复制到新工程
3. **视图迁移** - 将 views 目录转换并复制到新工程

**优势**：
- ✅ 安全性高，不会破坏原项目
- ✅ 支持分步迁移和验证
- ✅ 智能识别组件和视图类型
- ✅ 提供详细的迁移统计

### 🔄 传统迁移模式

**适用场景**：
- 希望直接在原项目上进行迁移
- 项目较小，风险可控
- 需要完整的 7 步迁移流程

**迁移流程**：
1. **升级依赖** - 将 Vue 相关依赖升级到 Vue 3 版本
2. **兼容性检查** - 检查第三方依赖是否支持 Vue 3
3. **代码迁移** - 使用 Gogocode 转换 .vue 和 .js 文件
4. **记录失败** - 记录转换失败的文件供后续处理
5. **AI 修复** - 使用 AI 自动修复转换失败的文件
6. **ESLint 修复** - 运行 ESLint 修复格式和语法问题
7. **构建修复** - 尝试构建项目并使用 AI 修复构建错误

## 📊 功能详解

### 🎯 智能迁移策略分析

智能策略分析是新增的核心功能，能够根据项目特点自动选择最佳迁移策略：

#### 策略选择逻辑

**AI 辅助策略**（当有 AI token 时）：
- 🤖 使用 AI 进行智能代码分析和转换建议
- 🎯 针对具体使用场景提供精确的迁移方案
- 🔧 自动处理复杂的组件迁移逻辑
- 📝 生成个性化的代码修改建议

**文档指导策略**（当无 AI token 时）：
- 📖 生成详细的迁移指导文档
- 📋 提供分步骤的手动迁移指南
- 🔗 匹配现有的组件迁移文档
- ⏱️ 评估迁移复杂度和时间

#### 分析内容

```bash
📊 组件使用分析结果:
总依赖数: 11
有迁移文档: 6
无迁移文档: 5
扫描文件数: 1
找到使用: 2 处

需要迁移的依赖:
  ❓ ⚪ vue@^2.6.14 (vue-core)
  📖 ✅ vue-count-to@^1.0.13 (utility)
  📖 ✅ vuedraggable@^2.24.3 (utility)
  ❓ ⚪ element-ui@^2.15.9 (ui-library)
```

**图标说明**：
- 📖 = 有迁移文档
- ❓ = 无迁移文档
- ✅ = 在代码中有使用
- ⚪ = 在代码中未发现使用

### Package 对比功能

智能对比新旧工程的 package.json，识别：
- 需要添加的依赖
- 需要移除的依赖
- 需要更新的依赖版本
- Scripts 脚本差异

```bash
# 示例输出
📦 Dependencies 变化:
  ➕ 需要添加 (3):
    @element-plus/icons-vue@^2.3.1
    @vueuse/core@^12.0.0
    element-plus@^2.9.0
  ➖ 需要移除 (24):
    element-ui@2.13.2
    vue-template-compiler@2.6.10
    ...
  🔄 需要更新 (3):
    vue: 2.6.10 → ^3.5.13
    vue-router: 3.0.2 → ^4.5.0
    echarts: 4.2.1 → ^5.6.0
```

### 组件迁移功能

专门的组件迁移器，支持：
- **组件类型识别**：基础组件、表单组件、表格组件、图表组件、布局组件、业务组件
- **Element UI 转换**：自动转换 Element UI 到 Element Plus
- **图标转换**：处理图标系统的变化
- **组件特定优化**：针对不同类型组件的专门转换规则

### 视图迁移功能

专门的视图迁移器，支持：
- **页面类型识别**：列表页面、详情页面、表单页面、仪表板页面、认证页面、错误页面
- **路由检测**：自动发现路由文件
- **状态管理检测**：自动发现 Vuex/Pinia 文件
- **页面特定转换**：针对不同类型页面的专门转换规则

## 🔧 环境配置

### AI 功能配置

```bash
# .env 文件配置
DEEPSEEK_TOKEN=your_deepseek_token    # 推荐，性价比高
GLM_TOKEN=your_glm_token              # 备选
OPENAI_API_KEY=your_openai_key        # 备选
```

### 目标技术栈

本工具支持迁移到以下技术栈：
- **Vue 3** + Composition API
- **Vue Router 4**
- **Vuex 4** 或 **Pinia**
- **Element Plus**
- **TypeScript** 支持

## 📈 迁移统计示例

```bash
🧩 组件迁移统计:
总组件数: 33

组件类型分布:
  基础组件: 29 个
  表格组件: 1 个
  图表组件: 3 个

📄 视图迁移统计:
总视图数: 86

视图类型分布:
  列表页面: 41 个
  详情页面: 1 个
  表单页面: 7 个
  仪表板页面: 6 个
  认证页面: 1 个
  错误页面: 3 个
  其他页面: 27 个

📍 发现路由文件:
  src/router/index.js

🗃️  发现状态管理文件:
  src/store/index.js
```

## 🎯 最佳实践

### 智能迁移建议流程（推荐）

1. **策略分析阶段**
   ```bash
   # 首先分析项目，获得迁移策略
   node bin/vue-migrator.js analyze /path/to/vue2-project --verbose

   # 如果有 AI token，使用 AI 辅助分析
   node bin/vue-migrator.js analyze /path/to/vue2-project --ai-key your_token
   ```

2. **准备阶段**
   ```bash
   # 根据分析结果创建 Vue 3 新工程
   npm create vue@latest my-vue3-project
   cd my-vue3-project
   npm install

   # 根据生成的迁移指导文档进行准备工作
   # 查看 migration-guide.md 了解详细计划
   ```

3. **对比分析**
   ```bash
   # 对比新旧工程差异
   node bin/vue-migrator.js compare ../old-project ../new-project
   ```

4. **分步迁移**
   ```bash
   # 先迁移组件（根据迁移指导文档的建议顺序）
   node bin/vue-migrator.js migrate-components ../old-project ../new-project --dry-run

   # 确认无误后正式迁移
   node bin/vue-migrator.js migrate-components ../old-project ../new-project

   # 再迁移视图
   node bin/vue-migrator.js migrate-views ../old-project ../new-project
   ```

5. **验证测试**
   ```bash
   # 在新工程中测试
   cd ../new-project
   npm run dev
   npm run build
   ```

### 注意事项

- 🔍 **迁移前备份**：虽然新工程模式不会修改原项目，但建议备份重要数据
- 🧪 **分步验证**：建议分步迁移，每步都进行验证
- 🔧 **手动调整**：某些复杂逻辑可能需要手动调整
- 📝 **文档更新**：迁移完成后记得更新项目文档

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！

## 📄 许可证

MIT License

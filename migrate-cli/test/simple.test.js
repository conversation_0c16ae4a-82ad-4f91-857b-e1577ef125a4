#!/usr/bin/env node

/**
 * 简化的测试文件，用于快速验证核心功能
 */

const fs = require('fs-extra');
const path = require('path');

process.env.DEEPSEEK_TOKEN = 'test-token';

const { AIService } = require('../src/ai/ai-service');
const BuildFixer = require('../src/buildFixer');
const AIRepairer = require('../src/aiRepairer');

describe('Simplified Test Suite', () => {

  describe('AIService Basic Functionality', () => {
    let aiService;

    beforeEach(() => {
      aiService = new AIService({
        maxTokens: 1000,
        temperature: 0.1
      });
    });

    it('should have correct configuration', () => {
      expect(aiService.options.maxTokens).toBe(1000);
      expect(aiService.options.temperature).toBe(0.1);
    });

    it('should have a working isEnabled() method', () => {
      expect(typeof aiService.isEnabled()).toBe('boolean');
    });

    it('should have a working getStats() method', () => {
      const stats = aiService.getStats();
      expect(typeof stats).toBe('object');
      expect(stats.attempted).toBe(0);
    });

    it('should have a working validateRepairedContent() method', () => {
      const validContent = aiService.validateRepairedContent(
        'export default { name: "Test" }',
        'export default { name: "Original" }'
      );
      expect(validContent).toBe(true);
    });
  });

  describe('BuildFixer Basic Functionality', () => {
    const tempDir = path.join(__dirname, 'temp-simple');
    let buildFixer;

    beforeAll(async () => {
      await fs.ensureDir(tempDir);
    });

    afterAll(async () => {
      await fs.remove(tempDir);
    });

    beforeEach(() => {
      buildFixer = new BuildFixer(tempDir, {
        buildCommand: 'echo "test build"',
        maxRetries: 2
      });
    });

    it('should have correct configuration', () => {
      expect(buildFixer.projectPath).toBe(tempDir);
      expect(buildFixer.options.maxRetries).toBe(2);
    });

    it('should have a working detectErrorStart() method', () => {
      const tsError = buildFixer.detectErrorStart('src/test.ts(10,5): error TS2304: Cannot find name "Vue".');
      expect(tsError.type).toBe('typescript');
      expect(tsError.file).toBe('src/test.ts');
    });

    it('should have a working categorizeErrors() method', () => {
      const errors = [{
        message: 'Cannot find module "element-ui"',
        type: 'webpack'
      }];
      const categorized = buildFixer.categorizeErrors(errors);
      expect(categorized[0].category).toBe('missing-module');
    });

    it('should have a working generateBuildErrorPrompt() method', () => {
      const error = {
        type: 'vue',
        category: 'ui-library',
        message: 'el-button not found',
        file: 'test.vue'
      };
      const prompt = buildFixer.generateBuildErrorPrompt('test content', error);
      expect(prompt).toContain('构建错误修复');
    });

    it('should have a working getBuildStats() method', () => {
      const buildStats = buildFixer.getBuildStats();
      expect(typeof buildStats).toBe('object');
      expect(buildStats.buildAttempts).toBe(0);
    });
  });

  describe('AIRepairer Basic Functionality', () => {
    let aiRepairer;

    beforeEach(() => {
      aiRepairer = new AIRepairer({
        maxTokens: 2000,
        temperature: 0.2
      });
    });

    it('should have correct configuration', () => {
      expect(aiRepairer.options.maxTokens).toBe(2000);
      expect(aiRepairer.options.temperature).toBe(0.2);
    });

    it('should have a working generateRepairPrompt() method', () => {
      const failedFile = {
        file: 'test.vue',
        error: 'Cannot find module "element-ui"'
      };
      const prompt = aiRepairer.generateRepairPrompt('test content', failedFile);
      expect(prompt).toContain('Vue 2 + Element UI');
    });
  });

  describe('Integration Tests', () => {
    const tempDir = path.join(__dirname, 'temp-integration');
    let aiRepairer;
    let buildFixer;

    beforeAll(async () => {
      await fs.ensureDir(tempDir);
    });

    afterAll(async () => {
      await fs.remove(tempDir);
    });

    beforeEach(() => {
      aiRepairer = new AIRepairer();
      buildFixer = new BuildFixer(tempDir, {
        buildCommand: 'echo "test"',
        aiRepairer: aiRepairer
      });
    });

    it('should integrate BuildFixer and AIRepairer correctly', () => {
      expect(buildFixer.options.aiRepairer).toBe(aiRepairer);
    });

    it('should ensure BuildFixer inherits from AIService', () => {
      expect(buildFixer).toBeInstanceOf(AIService);
    });
  });
});

const { AIService } = require('../src/ai/ai-service');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('AIService', () => {
  let service;

  beforeEach(() => {
    service = new AIService();
  });

  it('should construct with default options', () => {
    expect(service.options.maxTokens).toBe(8000);
    expect(service.options.temperature).toBe(0);
    expect(service.options.maxRetries).toBe(3);
  });

  it('should have a working isEnabled() method', () => {
    expect(typeof service.isEnabled()).toBe('boolean');
  });

  it('should validate repaired content correctly', () => {
    const valid = service.validateRepairedContent(
      'export default { name: "Test" }',
      'export default { name: "Original" }'
    );
    expect(valid).toBe(true);
  });

  it('should get initial stats correctly', () => {
    const stats = service.getStats();
    expect(typeof stats).toBe('object');
    expect(stats.attempted).toBe(0);
  });
});
